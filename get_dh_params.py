#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DH参数获取工具
用于获取INEXBOT机械臂的DH(Denavit-Hartenberg)参数

DH参数是机器人运动学中用于描述连杆和关节关系的标准参数，包括：
- L1-L7: 连杆长度参数
- 其他运动学参数如转换比例、方向等

使用方法:
1. 确保机器人控制器已启动并可连接
2. 修改ROBOT_IP为实际的机器人IP地址
3. 运行脚本获取DH参数
"""

import sys
import os
import time

# 添加lib目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'lib'))

try:
    import nrc_interface as nrc
except ImportError as e:
    print(f"❌ 导入nrc_interface失败: {e}")
    print("请确保lib目录下有nrc_interface.py和nrc_host.pyd文件")
    sys.exit(1)

# 配置参数
ROBOT_IP = "************"  # 请修改为实际的机器人IP地址
ROBOT_PORT = "6001"
ROBOT_NUMBER = 0  # 机器人编号，默认为0


def connect_robot():
    """连接机器人控制器"""
    print(f"🔗 正在连接机器人控制器 {ROBOT_IP}:{ROBOT_PORT}...")
    
    socket_fd = nrc.connect_robot(ROBOT_IP, ROBOT_PORT)
    if socket_fd <= 0:
        print(f"❌ 连接失败，错误码: {socket_fd}")
        print("请检查:")
        print("1. 机器人控制器是否已启动")
        print("2. IP地址是否正确")
        print("3. 网络连接是否正常")
        return None
    
    print(f"✅ 连接成功，Socket ID: {socket_fd}")
    return socket_fd


def get_dh_parameters(socket_fd, robot_num=0):
    """
    获取机器人DH参数
    
    Args:
        socket_fd: 套接字文件描述符
        robot_num: 机器人编号，默认为0
    
    Returns:
        RobotDHParam对象或None
    """
    print(f"📊 正在获取机器人#{robot_num}的DH参数...")
    
    try:
        # 创建DH参数对象
        dh_param = nrc.RobotDHParam()
        
        # 调用获取DH参数的函数
        if robot_num == 0:
            result = nrc.get_robot_dh_param(socket_fd, dh_param)
        else:
            result = nrc.get_robot_dh_param_robot(socket_fd, robot_num, dh_param)
        
        if result == 0:
            print("✅ DH参数获取成功")
            return dh_param
        else:
            print(f"❌ 获取DH参数失败，错误码: {result}")
            return None
            
    except Exception as e:
        print(f"❌ 获取DH参数时发生异常: {e}")
        return None


def print_dh_parameters(dh_param):
    """
    打印DH参数信息
    
    Args:
        dh_param: RobotDHParam对象
    """
    if dh_param is None:
        print("❌ DH参数为空，无法显示")
        return
    
    print("\n" + "="*60)
    print("📋 机器人DH参数详情")
    print("="*60)
    
    # 连杆长度参数
    print("\n🔧 连杆长度参数 (Link Parameters):")
    print(f"  L1: {dh_param.L1:.6f} mm")
    print(f"  L2: {dh_param.L2:.6f} mm") 
    print(f"  L3: {dh_param.L3:.6f} mm")
    print(f"  L4: {dh_param.L4:.6f} mm")
    print(f"  L5: {dh_param.L5:.6f} mm")
    print(f"  L6: {dh_param.L6:.6f} mm")
    print(f"  L7: {dh_param.L7:.6f} mm")
    
    # 转换比例参数
    print("\n⚙️ 转换比例参数 (Conversion Ratios):")
    print(f"  X轴转换比例: {dh_param.conversionratio_x:.6f}")
    print(f"  Y轴转换比例: {dh_param.conversionratio_y:.6f}")
    print(f"  Z轴转换比例: {dh_param.conversionratio_z:.6f}")
    print(f"  J1关节转换比例: {dh_param.conversionratio_J1:.6f}")
    print(f"  J2关节转换比例: {dh_param.conversionratio_J2:.6f}")
    print(f"  J3关节转换比例: {dh_param.conversionratio_J3:.6f}")
    
    # 方向参数
    print("\n🧭 方向参数 (Direction Parameters):")
    print(f"  三轴方向: {dh_param.threeAxisDirection}")
    print(f"  五轴方向: {dh_param.fiveAxisDirection}")
    print(f"  倒置状态: {dh_param.upsideDown}")
    
    # 其他参数
    print("\n📐 其他参数 (Other Parameters):")
    print(f"  二轴转换比例: {dh_param.twoAxisConversionRatio:.6f}")
    print(f"  三轴转换比例: {dh_param.threeAxisConversionRatio:.6f}")
    print(f"  放大比例: {dh_param.amplificationRatio:.6f}")
    
    # 如果有升降相关参数
    try:
        print(f"  升降导程值: {dh_param.uplift_lead_value:.6f}")
        print(f"  喷涂距离: {dh_param.spray_distance:.6f}")
    except AttributeError:
        # 某些版本可能没有这些参数
        pass


def save_dh_parameters_to_file(dh_param, filename="dh_parameters.txt"):
    """
    将DH参数保存到文件
    
    Args:
        dh_param: RobotDHParam对象
        filename: 保存的文件名
    """
    if dh_param is None:
        print("❌ DH参数为空，无法保存")
        return False
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("INEXBOT机器人DH参数\n")
            f.write("="*50 + "\n")
            f.write(f"获取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("连杆长度参数 (mm):\n")
            f.write(f"L1 = {dh_param.L1:.6f}\n")
            f.write(f"L2 = {dh_param.L2:.6f}\n")
            f.write(f"L3 = {dh_param.L3:.6f}\n")
            f.write(f"L4 = {dh_param.L4:.6f}\n")
            f.write(f"L5 = {dh_param.L5:.6f}\n")
            f.write(f"L6 = {dh_param.L6:.6f}\n")
            f.write(f"L7 = {dh_param.L7:.6f}\n\n")
            
            f.write("转换比例参数:\n")
            f.write(f"conversionratio_x = {dh_param.conversionratio_x:.6f}\n")
            f.write(f"conversionratio_y = {dh_param.conversionratio_y:.6f}\n")
            f.write(f"conversionratio_z = {dh_param.conversionratio_z:.6f}\n")
            f.write(f"conversionratio_J1 = {dh_param.conversionratio_J1:.6f}\n")
            f.write(f"conversionratio_J2 = {dh_param.conversionratio_J2:.6f}\n")
            f.write(f"conversionratio_J3 = {dh_param.conversionratio_J3:.6f}\n\n")
            
            f.write("方向参数:\n")
            f.write(f"threeAxisDirection = {dh_param.threeAxisDirection}\n")
            f.write(f"fiveAxisDirection = {dh_param.fiveAxisDirection}\n")
            f.write(f"upsideDown = {dh_param.upsideDown}\n\n")
            
            f.write("其他参数:\n")
            f.write(f"twoAxisConversionRatio = {dh_param.twoAxisConversionRatio:.6f}\n")
            f.write(f"threeAxisConversionRatio = {dh_param.threeAxisConversionRatio:.6f}\n")
            f.write(f"amplificationRatio = {dh_param.amplificationRatio:.6f}\n")
        
        print(f"✅ DH参数已保存到文件: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False


def main():
    """主函数"""
    print("🤖 INEXBOT机器人DH参数获取工具")
    print("="*50)
    
    # 1. 连接机器人
    socket_fd = connect_robot()
    if socket_fd is None:
        return
    
    try:
        # 2. 获取DH参数
        dh_param = get_dh_parameters(socket_fd, ROBOT_NUMBER)
        
        if dh_param is not None:
            # 3. 显示DH参数
            print_dh_parameters(dh_param)
            
            # 4. 询问是否保存到文件
            save_choice = input("\n💾 是否将DH参数保存到文件? (y/n): ").strip().lower()
            if save_choice in ['y', 'yes', '是']:
                filename = input("📁 请输入文件名 (默认: dh_parameters.txt): ").strip()
                if not filename:
                    filename = "dh_parameters.txt"
                save_dh_parameters_to_file(dh_param, filename)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行过程中发生错误: {e}")
    finally:
        # 5. 断开连接
        try:
            nrc.disconnect_robot(socket_fd)
            print("🔌 已断开机器人连接")
        except:
            pass


if __name__ == "__main__":
    main()
